// 认证 API
const CORRECT_PASSWORD = 'LongDz6299'; // 设置你的密码

export async function onRequestPost(context) {
    const { request, handleCORS } = context;
    
    try {
        const { password } = await request.json();
        
        return new Response(
            JSON.stringify({
                success: password === CORRECT_PASSWORD
            }), 
            {
                headers: {
                    'Content-Type': 'application/json',
                    ...handleCORS(request)
                },
            }
        );
    } catch (error) {
        return new Response(
            JSON.stringify({ success: false, error: 'Invalid request' }), 
            {
                status: 400,
                headers: {
                    'Content-Type': 'application/json',
                    ...handleCORS(request)
                },
            }
        );
    }
}
