// HTML 内容直接嵌入
const html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memos 管理</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        textarea { width: 100%; height: 100px; margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .memo-item { background: white; margin: 10px 0; padding: 15px; border-radius: 4px; border-left: 4px solid #007cba; }
        .memo-time { color: #666; font-size: 0.9em; }
        .memo-content { margin: 10px 0; }
        .memo-actions { margin-top: 10px; }
        .memo-actions button { margin-right: 10px; font-size: 0.9em; padding: 5px 10px; }
        .delete-btn { background: #dc3545; }
        .delete-btn:hover { background: #c82333; }
        .edit-btn { background: #28a745; }
        .edit-btn:hover { background: #218838; }
        .auth-container { text-align: center; margin-top: 50px; }
        .hidden { display: none; }
        .error { color: #dc3545; margin: 10px 0; }
        .success { color: #28a745; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="auth-container" id="authContainer">
        <h2>请输入密码</h2>
        <input type="password" id="passwordInput" placeholder="输入密码">
        <button onclick="authenticate()">登录</button>
        <div id="authError" class="error hidden"></div>
    </div>

    <div class="container hidden" id="mainContainer">
        <h1>Memos 管理</h1>

        <div>
            <h3>发布新 Memo</h3>
            <textarea id="newMemoContent" placeholder="写点什么..."></textarea>
            <button onclick="publishMemo()">发布</button>
            <div id="publishMessage"></div>
        </div>

        <div>
            <h3>已发布的 Memos</h3>
            <button onclick="loadMemos()">刷新列表</button>
            <div id="memosList"></div>
        </div>
    </div>

    <script>
        let authToken = '';

        async function authenticate() {
            const password = document.getElementById('passwordInput').value;
            const errorDiv = document.getElementById('authError');

            try {
                const response = await fetch('/api/auth', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ password })
                });

                const result = await response.json();

                if (result.success) {
                    authToken = password;
                    document.getElementById('authContainer').classList.add('hidden');
                    document.getElementById('mainContainer').classList.remove('hidden');
                    loadMemos();
                } else {
                    errorDiv.textContent = '密码错误';
                    errorDiv.classList.remove('hidden');
                }
            } catch (error) {
                errorDiv.textContent = '认证失败: ' + error.message;
                errorDiv.classList.remove('hidden');
            }
        }

        async function publishMemo() {
            const content = document.getElementById('newMemoContent').value;
            const messageDiv = document.getElementById('publishMessage');

            if (!content.trim()) {
                messageDiv.innerHTML = '<div class="error">内容不能为空</div>';
                return;
            }

            try {
                const response = await fetch('/api/memos', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authToken
                    },
                    body: JSON.stringify({ content })
                });

                if (response.ok) {
                    document.getElementById('newMemoContent').value = '';
                    messageDiv.innerHTML = '<div class="success">发布成功!</div>';
                    loadMemos();
                } else {
                    messageDiv.innerHTML = '<div class="error">发布失败</div>';
                }
            } catch (error) {
                messageDiv.innerHTML = '<div class="error">发布失败: ' + error.message + '</div>';
            }
        }

        async function loadMemos() {
            try {
                const response = await fetch('/api/memos?limit=50');
                const result = await response.json();

                const listDiv = document.getElementById('memosList');
                listDiv.innerHTML = '';

                result.data.forEach(memo => {
                    const memoDiv = document.createElement('div');
                    memoDiv.className = 'memo-item';
                    memoDiv.innerHTML = \`
                        <div class="memo-time">\${new Date(memo.createTime).toLocaleString()}</div>
                        <div class="memo-content">\${memo.content}</div>
                        <div class="memo-actions">
                            <button class="edit-btn" onclick="editMemo('\${memo.uid}', '\${memo.content.replace(/'/g, "\\\\'")}')">编辑</button>
                            <button class="delete-btn" onclick="deleteMemo('\${memo.uid}')">删除</button>
                        </div>
                    \`;
                    listDiv.appendChild(memoDiv);
                });
            } catch (error) {
                document.getElementById('memosList').innerHTML = '<div class="error">加载失败: ' + error.message + '</div>';
            }
        }

        async function editMemo(uid, currentContent) {
            const newContent = prompt('编辑内容:', currentContent);
            if (newContent === null || newContent.trim() === '') return;

            try {
                const response = await fetch(\`/api/memos/\${uid}\`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authToken
                    },
                    body: JSON.stringify({ content: newContent })
                });

                if (response.ok) {
                    loadMemos();
                } else {
                    alert('编辑失败');
                }
            } catch (error) {
                alert('编辑失败: ' + error.message);
            }
        }

        async function deleteMemo(uid) {
            if (!confirm('确定要删除这条 memo 吗？')) return;

            try {
                const response = await fetch(\`/api/memos/\${uid}\`, {
                    method: 'DELETE',
                    headers: { 'Authorization': authToken }
                });

                if (response.ok) {
                    loadMemos();
                } else {
                    alert('删除失败');
                }
            } catch (error) {
                alert('删除失败: ' + error.message);
            }
        }

        // 回车键快捷发布
        document.getElementById('passwordInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') authenticate();
        });

        document.getElementById('newMemoContent').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && e.ctrlKey) publishMemo();
        });
    </script>
</body>
</html>`;

const CORRECT_PASSWORD = 'LongDz6299'; // 设置你的密码
const CALLBACK_URL = 'https://vercel.com/jimuzhes-projects/astro-blog-master'; // 设置回调 URL
const ALLOWED_ORIGINS = ['https://blog.name666.top', 'https://memos.name666.top']; // 允许请求的域名

// 生成随机 UID
function generateUID() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 22; i++) {
        const randomIndex = Math.floor(Math.random() * chars.length);
        result += chars[randomIndex];
    }
    return result;
}
// CORS 处理
function handleCORS(request) {
    const origin = request.headers.get('Origin');
    const allowedOrigin = ALLOWED_ORIGINS.includes(origin) ? origin : ALLOWED_ORIGINS[0];

    const corsHeaders = {
        'Access-Control-Allow-Origin': allowedOrigin,
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400',
    };
    return corsHeaders;
}

function getCurrentTimeInISOFormat() {
    const now = new Date();
    // 获取各个部分
    const year = now.getUTCFullYear();
    const month = String(now.getUTCMonth() + 1).padStart(2, '0'); // 月份从零开始
    const day = String(now.getUTCDate()).padStart(2, '0');
    const hours = String(now.getUTCHours()).padStart(2, '0');
    const minutes = String(now.getUTCMinutes()).padStart(2, '0');
    const seconds = String(now.getUTCSeconds()).padStart(2, '0');
    // 组装成 ISO 8601 格式字符串
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;
}
async function handleRequest(request, env) {
    const url = new URL(request.url);

    function validateAuth(request) {
        const auth = request.headers.get('Authorization');
        return auth === CORRECT_PASSWORD;
    }
    async function shouldNotify(uid) {
        const indexStr = await env.KV.get('index');
        if (!indexStr) return false;
        const index = JSON.parse(indexStr);
        return index.indexOf(uid) < 10;
    }
    async function executeCallback() {
        try {
            await fetch(CALLBACK_URL);
        } catch (error) {
            console.error('Callback failed:', error);
        }
    }
    const corsHeaders = handleCORS(request);

    // 处理 CORS 预检请求
    if (request.method === 'OPTIONS') {
        return new Response(null, {
            headers: handleCORS(request),
        });
    }
    // 根路径和管理页面
    if (url.pathname === '/' || url.pathname === '/manage') {
        return new Response(html, {
            headers: {
                'Content-Type': 'text/html'
            },
        });
    }
    // 验证密码
    if (url.pathname === '/api/auth' && request.method === 'POST') {
        const {
            password
        } = await request.json();
        return new Response(
            JSON.stringify({
                success: password === CORRECT_PASSWORD
            }), {
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
            }
        );
    }
    // API 路由处理
    if (url.pathname.startsWith('/api/memos')) {
        // 获取说说列表
        if (request.method === 'GET') {
            const offset = parseInt(url.searchParams.get('offset')) || 0;
            const limit = parseInt(url.searchParams.get('limit')) || 10;
            const indexStr = await env.KV.get('index');
            const index = indexStr ? JSON.parse(indexStr) : [];
            const pageUids = index.slice(offset, offset + limit);
            const posts = await Promise.all(
                pageUids.map(uid => env.KV.get(uid).then(JSON.parse))
            );
            return new Response(JSON.stringify({
                offset,
                limit,
                data: posts,
                total: index.length,
                hasMore: (offset + limit) < index.length,
            }), {
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
            });
        }
        // 需要验证的操作
        if (!validateAuth(request)) {
            return new Response('Unauthorized', {
                status: 401,
                headers: corsHeaders
            });
        }
        // 发布新说说
        if (request.method === 'POST') {
            const {
                content
            } = await request.json();
            if (!content || !content.trim()) {
                return new Response('Content cannot be empty', {
                    status: 400,
                    headers: corsHeaders
                });
            }
            const indexStr = await env.KV.get('index');
            const index = indexStr ? JSON.parse(indexStr) : [];
            let uid = generateUID();
            while (true) {
                if (!index.includes(uid)) {
                    break;
                }
                uid = generateUID();
            }
            const post = {
                uid,
                createTime: getCurrentTimeInISOFormat(),
                content: content.trim()
            };
            index.unshift(uid);
            await Promise.all([
                env.KV.put('index', JSON.stringify(index)),
                env.KV.put(uid, JSON.stringify(post))
            ]);
            await executeCallback();
            return new Response(JSON.stringify(post), {
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
            });
        }
        // 编辑说说
        if (request.method === 'PUT') {
            const uid = url.pathname.split('/').pop();
            const {
                content
            } = await request.json();
            if (!content || !content.trim()) {
                return new Response('Content cannot be empty', {
                    status: 400,
                    headers: corsHeaders
                });
            }
            const postStr = await env.KV.get(uid);
            if (!postStr) {
                return new Response('Post not found', {
                    status: 404,
                    headers: corsHeaders
                });
            }
            const post = JSON.parse(postStr);
            post.content = content.trim();
            await env.KV.put(uid, JSON.stringify(post));
            // 检查是否需要回调
            if (await shouldNotify(uid)) {
                await executeCallback();
            }
            return new Response(JSON.stringify(post), {
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
            });
        }
        // 删除说说
        if (request.method === 'DELETE') {
            const uid = url.pathname.split('/').pop();
            const indexStr = await env.KV.get('index');
            if (!indexStr) {
                return new Response('Post not found', {
                    status: 404,
                    headers: corsHeaders
                });
            }
            const needCallback = await shouldNotify(uid);
            const index = JSON.parse(indexStr);
            const newIndex = index.filter(id => id !== uid);
            await Promise.all([
                env.KV.put('index', JSON.stringify(newIndex)),
                env.KV.delete(uid)
            ]);
            if (needCallback) {
                await executeCallback();
            }
            return new Response(JSON.stringify({
                success: true
            }), {
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
            });
        }
    }
    return new Response('Not Found', {
        status: 404,
        headers: corsHeaders
    });
}
export default {
    async fetch(request, env) {
        try {
            return handleRequest(request, env);
        } catch (error) {
            return new Response(`Internal Server Error: ${error.message}`, {
                status: 500,
                headers: handleCORS(request)
            });
        }
    },
};