import html from './index.html';

const CORRECT_PASSWORD = 'LongDz6299'; // 设置你的密码    // [!code highlight]
const CALLBACK_URL = 'https://vercel.com/jimuzhes-projects/astro-blog-master'; // 设置回调 URL   // [!code highlight]
const ALLOWED_ORIGINS = ['https://blog.name666.top','https://memos-api.3106628438.workers.dev','https://memos.name666.top']; // 允许请求的域名  // [!code highlight]

// 生成随机 UID
function generateUID() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 22; i++) {
        const randomIndex = Math.floor(Math.random() * chars.length);
        result += chars[randomIndex];
    }
    return result;
}
// CORS 处理
function handleCORS(request) {
    const origin = request.headers.get('Origin');
    const allowedOrigin = ALLOWED_ORIGINS.includes(origin) ? origin : ALLOWED_ORIGINS[0];

    const corsHeaders = {
        'Access-Control-Allow-Origin': allowedOrigin,
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400',
    };
    return corsHeaders;
}

function getCurrentTimeInISOFormat() {
    const now = new Date();
    // 获取各个部分
    const year = now.getUTCFullYear();
    const month = String(now.getUTCMonth() + 1).padStart(2, '0'); // 月份从零开始
    const day = String(now.getUTCDate()).padStart(2, '0');
    const hours = String(now.getUTCHours()).padStart(2, '0');
    const minutes = String(now.getUTCMinutes()).padStart(2, '0');
    const seconds = String(now.getUTCSeconds()).padStart(2, '0');
    // 组装成 ISO 8601 格式字符串
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;
}
async function handleRequest(request, env) {
    const url = new URL(request.url);

    function validateAuth(request) {
        const auth = request.headers.get('Authorization');
        return auth === CORRECT_PASSWORD;
    }
    async function shouldNotify(uid) {
        const indexStr = await env.KV.get('index');
        if (!indexStr) return false;
        const index = JSON.parse(indexStr);
        return index.indexOf(uid) < 10;
    }
    async function executeCallback() {
        try {
            await fetch(CALLBACK_URL);
        } catch (error) {
            console.error('Callback failed:', error);
        }
    }
    const corsHeaders = handleCORS(request);

    // 处理 CORS 预检请求
    if (request.method === 'OPTIONS') {
        return new Response(null, {
            headers: handleCORS(request),
        });
    }
    // 管理页面
    if (url.pathname === '/manage') {
        return new Response(html, {
            headers: {
                'Content-Type': 'text/html'
            },
        });
    }
    // 验证密码
    if (url.pathname === '/api/auth' && request.method === 'POST') {
        const {
            password
        } = await request.json();
        return new Response(
            JSON.stringify({
                success: password === CORRECT_PASSWORD
            }), {
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
            }
        );
    }
    // API 路由处理
    if (url.pathname.startsWith('/api/memos')) {
        // 获取说说列表
        if (request.method === 'GET') {
            const offset = parseInt(url.searchParams.get('offset')) || 0;
            const limit = parseInt(url.searchParams.get('limit')) || 10;
            const indexStr = await env.KV.get('index');
            const index = indexStr ? JSON.parse(indexStr) : [];
            const pageUids = index.slice(offset, offset + limit);
            const posts = await Promise.all(
                pageUids.map(uid => env.KV.get(uid).then(JSON.parse))
            );
            return new Response(JSON.stringify({
                offset,
                limit,
                data: posts,
                total: index.length,
                hasMore: (offset + limit) < index.length,
            }), {
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
            });
        }
        // 需要验证的操作
        if (!validateAuth(request)) {
            return new Response('Unauthorized', {
                status: 401,
                headers: corsHeaders
            });
        }
        // 发布新说说
        if (request.method === 'POST') {
            const {
                content
            } = await request.json();
            if (!content || !content.trim()) {
                return new Response('Content cannot be empty', {
                    status: 400,
                    headers: corsHeaders
                });
            }
            const indexStr = await env.KV.get('index');
            const index = indexStr ? JSON.parse(indexStr) : [];
            let uid = generateUID();
            while (true) {
                if (!index.includes(uid)) {
                    break;
                }
                uid = generateUID();
            }
            const post = {
                uid,
                createTime: getCurrentTimeInISOFormat(),
                content: content.trim()
            };
            index.unshift(uid);
            await Promise.all([
                env.KV.put('index', JSON.stringify(index)),
                env.KV.put(uid, JSON.stringify(post))
            ]);
            await executeCallback();
            return new Response(JSON.stringify(post), {
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
            });
        }
        // 编辑说说
        if (request.method === 'PUT') {
            const uid = url.pathname.split('/').pop();
            const {
                content
            } = await request.json();
            if (!content || !content.trim()) {
                return new Response('Content cannot be empty', {
                    status: 400,
                    headers: corsHeaders
                });
            }
            const postStr = await env.KV.get(uid);
            if (!postStr) {
                return new Response('Post not found', {
                    status: 404,
                    headers: corsHeaders
                });
            }
            const post = JSON.parse(postStr);
            post.content = content.trim();
            await env.KV.put(uid, JSON.stringify(post));
            // 检查是否需要回调
            if (await shouldNotify(uid)) {
                await executeCallback();
            }
            return new Response(JSON.stringify(post), {
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
            });
        }
        // 删除说说
        if (request.method === 'DELETE') {
            const uid = url.pathname.split('/').pop();
            const indexStr = await env.KV.get('index');
            if (!indexStr) {
                return new Response('Post not found', {
                    status: 404,
                    headers: corsHeaders
                });
            }
            const needCallback = await shouldNotify(uid);
            const index = JSON.parse(indexStr);
            const newIndex = index.filter(id => id !== uid);
            await Promise.all([
                env.KV.put('index', JSON.stringify(newIndex)),
                env.KV.delete(uid)
            ]);
            if (needCallback) {
                await executeCallback();
            }
            return new Response(JSON.stringify({
                success: true
            }), {
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
            });
        }
    }
    return new Response('Not Found', {
        status: 404,
        headers: corsHeaders
    });
}
export default {
    async fetch(request, env) {
        try {
            return handleRequest(request, env);
        } catch (error) {
            return new Response(`Internal Server Error: ${error.message}`, {
                status: 500,
                headers: handleCORS(request)
            });
        }
    },
};