// Memos API - 获取和创建
const CORRECT_PASSWORD = 'LongDz6299';
const CALLBACK_URL = 'https://vercel.com/jimuzhes-projects/astro-blog-master';

// 生成随机 UID
function generateUID() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 22; i++) {
        const randomIndex = Math.floor(Math.random() * chars.length);
        result += chars[randomIndex];
    }
    return result;
}

function getCurrentTimeInISOFormat() {
    const now = new Date();
    const year = now.getUTCFullYear();
    const month = String(now.getUTCMonth() + 1).padStart(2, '0');
    const day = String(now.getUTCDate()).padStart(2, '0');
    const hours = String(now.getUTCHours()).padStart(2, '0');
    const minutes = String(now.getUTCMinutes()).padStart(2, '0');
    const seconds = String(now.getUTCSeconds()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;
}

function validateAuth(request) {
    const auth = request.headers.get('Authorization');
    return auth === CORRECT_PASSWORD;
}

async function executeCallback() {
    try {
        await fetch(CALLBACK_URL);
    } catch (error) {
        console.error('Callback failed:', error);
    }
}

// GET - 获取说说列表
export async function onRequestGet(context) {
    const { request, env, handleCORS } = context;
    const url = new URL(request.url);
    
    const offset = parseInt(url.searchParams.get('offset')) || 0;
    const limit = parseInt(url.searchParams.get('limit')) || 10;
    
    const indexStr = await env.KV.get('index');
    const index = indexStr ? JSON.parse(indexStr) : [];
    const pageUids = index.slice(offset, offset + limit);
    const posts = await Promise.all(
        pageUids.map(uid => env.KV.get(uid).then(JSON.parse))
    );
    
    return new Response(JSON.stringify({
        offset,
        limit,
        data: posts,
        total: index.length,
        hasMore: (offset + limit) < index.length,
    }), {
        headers: {
            'Content-Type': 'application/json',
            ...handleCORS(request)
        },
    });
}

// POST - 发布新说说
export async function onRequestPost(context) {
    const { request, env, handleCORS } = context;
    
    if (!validateAuth(request)) {
        return new Response('Unauthorized', {
            status: 401,
            headers: handleCORS(request)
        });
    }
    
    try {
        const { content } = await request.json();
        
        if (!content || !content.trim()) {
            return new Response('Content cannot be empty', {
                status: 400,
                headers: handleCORS(request)
            });
        }
        
        const indexStr = await env.KV.get('index');
        const index = indexStr ? JSON.parse(indexStr) : [];
        
        let uid = generateUID();
        while (index.includes(uid)) {
            uid = generateUID();
        }
        
        const post = {
            uid,
            createTime: getCurrentTimeInISOFormat(),
            content: content.trim()
        };
        
        index.unshift(uid);
        await Promise.all([
            env.KV.put('index', JSON.stringify(index)),
            env.KV.put(uid, JSON.stringify(post))
        ]);
        
        await executeCallback();
        
        return new Response(JSON.stringify(post), {
            headers: {
                'Content-Type': 'application/json',
                ...handleCORS(request)
            },
        });
    } catch (error) {
        return new Response('Invalid request', {
            status: 400,
            headers: handleCORS(request)
        });
    }
}
