// Cloudflare Pages Functions 中间件
// 处理 CORS 和全局配置

const ALLOWED_ORIGINS = [
    'https://blog.name666.top', 
    'https://memos-api.pages.dev',
    'https://memos.name666.top'
];

// CORS 处理函数
function handleCORS(request) {
    const origin = request.headers.get('Origin');
    const allowedOrigin = ALLOWED_ORIGINS.includes(origin) ? origin : ALLOWED_ORIGINS[0];

    const corsHeaders = {
        'Access-Control-Allow-Origin': allowedOrigin,
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400',
    };
    return corsHeaders;
}

export async function onRequest(context) {
    const { request } = context;
    
    // 处理 CORS 预检请求
    if (request.method === 'OPTIONS') {
        return new Response(null, {
            headers: handleCORS(request),
        });
    }

    // 将 CORS 处理函数添加到 context 中，供其他函数使用
    context.handleCORS = handleCORS;
    
    // 继续处理请求
    return await context.next();
}
